<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/pages/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('pages_page'),
					'class' => 'btn btn-primary',
					)
				);

				if( isset($id) )
				{
					echo anchor('admin/pages', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('pages_pages'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-body table-responsive no-padding">
							<?php // var_dump($category); ?>
							<?php if( ! empty($pages) ): ?>
							<table class="table table-striped no-data-table">
								<colgroup>
									<col>
									<col width="120px">
									<col width="120px">
									<col width="120px">
									<col>
									<col width="50px">
								</colgroup>
								<thead>
									<th><?php echo lang('pages_name'); ?></th>
									<th><?php echo lang('pages_status'); ?></th>
									<th><?php echo lang('pages_type'); ?></th>
									<th><?php echo lang('pages_position'); ?></th>
									<th><?php echo lang('pages_categories'); ?></th>
									<th></th>
								</thead>
								<tbody>
								<?php foreach($pages as $page_id => $page): ?>
									<?php if( $id !== NULL && ! isset($categories[$page_id]) ) { continue; } ?>
									<tr>
										<td><?php echo html_escape($page->name); ?></td>
										<td><?php echo html_escape($status[$page->status]); ?></td>
										<td>
											<?php
											if( ! empty($page->href) ):
												echo lang('pages_href');
											elseif( isset($categories[$page_id]) ):
												echo html_escape($layout[$page->layout]);
											else:
												echo lang('pages_page');
											endif;
										?>
										</td>
										<td><?php echo html_escape($position[$page->position]); ?></td>
										<td>
											<?php if( isset($categories[$page_id]) ): ?>
												<?php foreach($categories[$page_id] as $category_id => $category_name): ?>
													<a href="<?php echo base_url('admin/pages/display/') . html_escape($category_id); ?>/"><span class="badge badge-primary"><?php echo html_escape($category_name); ?></span></a>
												<?php endforeach; ?>
											<?php endif; ?>
										</td>
										<td>
											<div class="btn-group btn-group-sm">
												<?php
												echo icon_anchor('admin/pages/update', $page_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
													array(
													'title' => lang('edit'),
													'class' => 'btn btn-default',
													)
												);
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php else: ?>
								<p class="margin"><?php echo lang('pages_empty'); ?></p>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');