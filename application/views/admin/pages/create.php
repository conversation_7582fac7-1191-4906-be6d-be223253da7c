<script type="text/javascript">
	window.addEventListener("load", function (){
		const urlParams = new URLSearchParams(window.location.search);
		const quickAction = urlParams.get('quick');
		if (quickAction == 'true') {
			$('select[name="pages_position"]').val('2');
			setTimeout(function() {
					$('button[data-widget="collapse"]')[0].click();
			}, 500);
		}
	})
</script>
<?php $this->load->view('template/header-iconpicker'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/pages', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('pages_create'); ?>
				<small></small>
			</h1>
		</section>

		<!-- Main content -->
		<section class="content">
			<?php
			echo form_open_multipart(NULL,array(
				'id' => 'form-company-group',
				'autocomplete' => 'off'
			));
			echo form_hidden('uuid_kvalprak',$uuid_kvalprak);
			?>
			<div class="row">
				<div class="col-lg-9">
					<?php echo validation_errors();	?>
					<div class="box">
						<div class="box-header with-border">
							<?php
							echo form_input(array(
									'name'	=> 'posts_name',
									'value'	=> set_value('posts_name'),
									'class' => 'form-control',
									'placeholder' => lang('posts_name')
								));
							?>
						</div>
						<div class="box-body">
						<?php
							echo form_textarea(array(
									'name' => 'pages_document',
									'id' => 'pages_document',
									'value' => set_value('pages_document', NULL, FALSE)
								));
						?>
						</div>
					</div>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('documents_attachments'); ?></h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-md-6">
									<div class="form-group file-upload no-margin">
										<div class="input-group">
											<span class="input-group-prepend"><i class="fa fa-file" aria-hidden="true"></i></span>
											<div id="dropzone" class="form-control dropzone"></div>
										</div>
									</div>
								</div>
								<div class="col-md-6">
									<?php if( !empty($attachments) ): ?>
									<table id="attachments-table" class="table table-striped table-bordered no-data-table">
										<colspan>
											<col>
											<col width="140px">
											<col width="100px">
										</colspan>
										<thead>
											<tr>
												<th>Filnamn</th>
												<th>Uppladdad</th>
												<th>Radera</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach($attachments as $attachment): ?>
												<tr>
													<td><?php echo html_escape($attachment->file_name); ?></td>
													<td><?php echo $attachment->uploaded_on; ?></td>
													<td>
														<div class="btn-group btn-group-sm">
															<?php
															echo form_button(array(
																	'title' => lang('delete'),
																	'class' => 'btn btn-default dialog-delete',
																	'data-id' => $attachment->attachment_id,
																	'data-title' => $attachment->file_name,
																	'data-dialog' => '#delete-dialog',
																	'data-url' => base_url('admin/pages/delete/attachments'),
																	'content' => '<i class="fa fa-trash" aria-hidden="true"></i> Radera'
																)
															);
															?>
														</div>
													</td>
												</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /.col-md-9-->
				<div class="col-lg-3">
					<div class="box">
						<div class="box-body">
							<div class="form-group">
								<?php echo form_label(lang('pages_status'),'pages_status'); ?>
								<p class="form-text"><?php echo lang('pages_status_help'); ?></p>
								<?php
									echo form_dropdown('pages_status', $status, set_value('pages_status'),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('pages_pageicon'),'pages_pageicon'); ?>
								<p class="form-text"><?php echo lang('pages_pageicon_help'); ?></p>
								<div class="input-group iconpicker-container">
									<?php
									echo form_input(array(
											'name'	=> 'pages_pageicon',
											'value'	=> set_value('pages_pageicon','fa-file-o'),
											'class' => 'form-control iconpicker',
											'data-placement' => 'bottomRight'
										));
									?>
									<span class="input-group-prepend"><i class="fa fa-file-o"></i></span>
								</div>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('pages_position'),'pages_position'); ?>
								<p class="form-text"><?php echo nl2br(lang('pages_position_help')); ?></p>
								<?php
									echo form_dropdown('pages_position', $position, set_value('pages_position'),array(
										'class' => 'form-control'
									));
								?>
							</div>
						</div>
					</div>
					<div class="box collapsed-box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('pages_href'); ?></h3>
							<div class="box-tools float-right">
								<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
							</div>
						</div>
						<div class="box-body">
							<div class="form-group">
								<?php echo form_label(lang('pages_href'),'pages_href'); ?>
								<p class="form-text"><?php echo nl2br(lang('pages_href_help')); ?></p>
								<p class="form-text"><?php echo nl2br(lang('pages_href_help_extended')); ?></p>
								<?php
									echo form_input(array(
										'name'  => 'pages_href',
										'value' => set_value('pages_href'),
										'class' => 'form-control'
									));
								?>
							</div>
						</div>
					</div>
					<div class="box collapsed-box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('pages_categories'); ?></h3>
							<div class="box-tools float-right">
								<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
							</div>
						</div>
						<div class="box-body">
							<p class="form-text"><?php echo nl2br(lang('pages_category_help')); ?></p>
							<?php foreach($categories[0] AS $category_id => $category_name): ?>
								<div class="form-group">
									<?php if( isset($categories[$category_id]) ): ?>
										<strong><?php echo $category_name; ?></strong>
										<?php foreach($categories[$category_id] AS $sub_category_id => $sub_category_name): ?>
											<div class="checkbox indent">
												<label>
													<?php echo form_checkbox('pages_category[]', $sub_category_id, set_checkbox('pages_category[]', $sub_category_id)) . $sub_category_name; ?>
												</label>
											</div>
										<?php endforeach; ?>
									<?php else: ?>
										<?php /*
										<div class="form-check">
											<label>
												<?php echo form_checkbox('pages_category[]', $category_id, set_checkbox('pages_category[]', $category_id)) . $category_name; ?>
											</label>
										</div>
										*/ ?>
									<?php endif; ?>
								</div>
							<?php endforeach; ?>
							<div class="form-group">
								<?php echo form_label(lang('pages_layout'),'pages_layout'); ?>
								<p class="form-text"><?php echo lang('pages_layout_help'); ?></p>
								<?php
									echo form_dropdown('pages_layout', $layout, set_value('pages_layout'),array(
										'class' => 'form-control'
									));
								?>
							</div>
						</div>
					</div>
				</div>
				<!-- /.col-md-4-->
			</div>
			<!-- /.row -->
			<?php echo form_close(); ?>
		</section>
		<!-- /.content -->
		<div id="delete-dialog" style="display:none" title="Inget filnamn.">
			<i class="fa fa-warning"></i> Vill du ta bort filen permanent?
		</div>
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer-pages'); ?>