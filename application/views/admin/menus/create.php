<?php $this->load->view('template/header-selectize-js'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				if( isset($folder_id) && isset($sub_folder_id) )
				{
					echo icon_anchor('admin/menus/view', array($folder_id,$sub_folder_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( isset($folder_id) && !isset($sub_folder_id) )
				{
					echo icon_anchor('admin/menus/view', array($folder_id), '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				
				if( !isset($folder_id) && !isset($sub_folder_id) )
				{
					echo anchor('admin/menus', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('menus_menus'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('add') . ' ' . lang('menus_'.$type); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('menus_menu_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('menus_name'),'menus_name'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_name_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'menus_name',
											'value'	=> set_value('menus_name'),
											'class' => 'form-control'
										));
								?>
							</div>
							<?php if($type === 'menu'): ?>
								<div class="form-group">
									<?php echo form_label(lang('menus_description'),'menus_description'); ?>
									<p class="form-text"><?php echo nl2br(lang('menus_description_help')); ?></p>
									<?php
										echo form_textarea(array(
												'name'	=> 'menus_description',
												'value'	=> set_value('menus_description'),
												'class' => 'form-control'
											));
									?>
								</div>
							<?php endif; ?>
							<div class="form-group">
								<?php echo form_label(lang('menus_sticky'),'menus_sticky'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_sticky_help')); ?></p>
								<p class="form-text"><?php echo nl2br(lang('menus_sticky_help_extended')); ?></p>
								<?php
									echo form_dropdown('menus_sticky', $menus_sticky, set_value('menus_sticky',0),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('groups_type_position'),'menus_position[]'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_type_position_help')); ?></p>
								<?php foreach($groups['position'] as $group_id => $group_name): ?>
								<div class="form-check">
									<label>
										<?php echo form_checkbox('menus_position[]', $group_id, set_checkbox('menus_position[]', $group_id)) . $group_name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('groups_type_department'),'menus_department[]'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_type_department_help')); ?></p>
								<?php
									$department_count = count($groups['department']);
									$department_checked = $department_count === 1 ? TRUE : FALSE;
									foreach($groups['department'] as $group_id => $group_name):
								?>
								<div class="form-check">
									<label>
										<?php echo form_checkbox('menus_department[]', $group_id, set_checkbox('menus_department[]', $group_id, $department_checked)) . $group_name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<?php if($type === 'menu' && $this->config->item('document_author')): ?>
								<div class="form-group">
									<?php echo form_label(lang('permissions_author'),'acl[]'); ?>
									<p class="form-text"><?php echo nl2br(lang('permissions_author_menu_help')); ?></p>
									<?php
										echo form_multiselect('acl[]', $users, set_value('acl[]'),array(
											'class' => 'form-control selectize',
										));
									?>
								</div>
							<?php endif; ?>
							<?php if($type === 'link'): ?>
								<div class="form-group">
									<?php echo form_label(lang('menus_href'),'menus_href'); ?>
									<p class="form-text"><?php echo nl2br(lang('menus_href_help')); ?></p>
									<?php
										echo form_input(array(
												'name'	=> 'menus_href',
												'value'	=> set_value('menus_href'),
												'class' => 'form-control'
											));
									?>
								</div>
							<?php endif; ?>
							<?php
								
							echo form_close();
							?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer-selectize-js');