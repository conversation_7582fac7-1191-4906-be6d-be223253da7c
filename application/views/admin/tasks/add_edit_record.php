<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/tasks/' . $type . 's', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('tasks_' . $type); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
						</div>
						<div class="box-body">
							<?php
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
                <?php echo form_label(lang('tasks_' . $type),'name'); ?>
                <?php
                  echo form_input(array(
                    'name'	=> 'name',
                    'value'	=> set_value('name', isset($name) ? $name : ''),
                    'class' => 'form-control',
                    'placeholder' => lang('tasks_' . $type),
                  ));
                ?>
              </div>
							<?php
							echo form_button(array(
								'type' => 'submit',
								'form' => 'form-company-group',
								'title' => lang('save'),
								'content' => lang('save')
							));
							?>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');