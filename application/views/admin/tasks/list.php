<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				echo anchor('admin/tasks/add/' . $type, '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('tasks_' . $type),
					'class' => 'btn btn-primary',
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('tasks_' . $type . 's'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
						</div>
						<div class="box-body no-padding">
							<table class="table table-striped no-data-table">
								<colgroup>
									<col>
									<col width="85px;">
								</colgroup>
								<tbody>
								<?php foreach($list as $id => $name): ?>
									<tr>
										<td>
											<?php echo html_escape($name); ?>
										</td>
										<td>
											<?php //var_dump($f); ?>
											<div class="btn-group btn-group-sm">
												<?php
												echo icon_anchor('admin/tasks/edit/' . $type, $id, '<i class="fa fa-pencil" aria-hidden="true"></i>',
													array(
													'title' => lang('edit'),
													'class' => 'btn btn-default',
													)
												);		
												echo icon_anchor('admin/tasks/delete/' . $type, $id, '<i class="fa fa-trash" aria-hidden="true"></i>',
													array(
													'title' => lang('delete'),
													'class' => 'btn btn-danger',
													)
												);											
												?>
											</div>
										</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');