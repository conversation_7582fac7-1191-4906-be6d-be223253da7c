<script type="text/javascript">
	let templatesData = {}
	function removeRow(e, id) {
		console.log(id)
		e.preventDefault();
		delete(templatesData[id]);
		$('#' + id).remove();
	}

	function removeExistingRow(e, id) {
		console.log(id)
		e.preventDefault();
		$('#' + id).find('input').first().val('delete');
		$('#' + id).hide();
	}

	function addRow(e) {
		e.preventDefault();
		let id = ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
			(c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
		);
		templatesData[id] = "";
		$("#multiTemplates").append(`
		<div style="display: flex; margin-bottom: 10px" id="${id}">
			<input type="file" accept=".docx, .xlsx, .pptx" name="other${id}" class="form-control">
			<button onclick="removeRow(event, '${id}')" class="btn btn-danger" title="Remove"><i class="fa fa-minus"></i></button>
		</div>
		`)
	}
</script>
<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
	<?php if($this->session->flashdata('message')): ?>
		<div class="mt-2" style="width: 90%;margin: auto;">
			<div class="alert alert-success">
					<?php echo $this->session->flashdata('message'); ?>
			</div>
		</div>
	<?php endif; ?>
	<?php if($this->session->flashdata('error')): ?>
		<div class="mt-2" style="width: 90%;margin: auto;">
			<div class="alert alert-error">
					<?php echo $this->session->flashdata('error'); ?>
			</div>
		</div>
	<?php endif; ?>
	<?php
			echo validation_errors();
			echo form_open_multipart(NULL,array(
				'id' => 'form-company-group',
				'autocomplete' => 'off'
			));
		?>
	<div class="row">
		<div class="col-sm-6 col-xc-12">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				if(isset($settings['docx']) || isset($settings['xlsx']) || isset($settings['pptx']) || isset($settings['other']))
				{
					echo anchor('admin/template/delete', '<i class="fa fa-trash" aria-hidden="true"></i>',
						array(
						'title' => lang('delete'),
						'class' => 'btn btn-danger'
						)
					);
				}

				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				?>
			</div>
			<h1>
				<?php echo lang('template_template'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div>
					<?php
						echo validation_errors();
					?>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('template_header'); ?></h3>
						</div>
						<div class="box-body">
							<div class="form-group">
								<?php echo form_label(lang('template_docx_upload'),'docx'); ?>
								<p class="form-text"><?php echo array_key_exists('docx_name', $settings) ? $settings['docx_name'] : ''; ?></p>
								<?php
									echo form_upload(array(
											'name'	=> 'docx',
											'accept' => '.docx',
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('template_xlsx_upload'),'xlsx'); ?>
								<p class="form-text"><?php echo array_key_exists('xlsx_name', $settings) ? $settings['xlsx_name'] : ''; ?></p>
								<?php
									echo form_upload(array(
											'name'	=> 'xlsx',
											'accept' => '.xlsx',
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('template_pptx_upload'),'pptx'); ?>
								<p class="form-text"><?php echo array_key_exists('pptx_name', $settings) ? $settings['pptx_name'] : ''; ?></p>
								<?php
									echo form_upload(array(
											'name'	=> 'pptx',
											'accept' => '.pptx',
											'class' => 'form-control'
										));
								?>
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
		</div>
		<div class="col-sm-6 col-xc-12" style="border-left: 1px solid lightgray;">
		<!-- Main content -->
		<section class="content">
			<div class="row">
					<div class="box">
						<div class="box-header with-border">
							<button onclick="addRow(event)" class="btn btn-success" title="Add" style="float: right">
								<i class="fa fa-plus"></i>
							</button>
							<h3 class="box-title"><?php echo lang('template_header_other'); ?></h3>
						</div>
						<div class="box-body">
							<?php 
							if (array_key_exists('other', $settings)) 
								foreach ($settings['other'] as $value) {
									$name_without_ext = substr($value['file'], 0, strlen($value['file'])-5);
									$extension = substr($value['file'], strlen($value['file'])-5, strlen($value['file']));
									echo '<div id="'.$name_without_ext.'">';
									echo '<input type="hidden" name="'. $value['file'] .'">';
									echo '<button onclick="removeExistingRow(event, \''. $name_without_ext .'\')" class="btn btn-danger float-right" title="Remove"><i class="fa fa-minus"></i></button>';
									echo '<p class="help-block">'.$value['name'].$extension.'</p>';
									echo '</div>';
								}
							?>
							<div id="multiTemplates">
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
		</div>
	</div>
	<div class="box-footer text-right">
							<?php
								echo form_button(array(
									'type' => 'submit',
									'form' => 'form-company-group',
									'class' => 'btn btn-primary',
									'title' => lang('save'),
									'content' => lang('save')
								));
							?>
						</div>
					<?php echo form_close(); ?>
	</div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');