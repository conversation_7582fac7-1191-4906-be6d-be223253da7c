<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/posts/create', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('posts_post'),
					'class' => 'btn btn-primary',
					)
				);
				
				if( isset($id) )
				{
					echo anchor('admin/posts', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('posts_posts'); ?>
				<small></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-body table-responsive no-padding">
							<?php // var_dump($category); ?>
							<?php if( ! empty($posts) ): ?>
							<table class="table table-striped no-data-table">
								<colgroup>
									<col>
									<col width="120px">
									<col width="120px">
									<col>
									<col width="50px">
								</colgroup>
								<thead>
									<th><?php echo lang('posts_name'); ?></th>
									<th><?php echo lang('posts_publish'); ?></th>
									<th><?php echo lang('posts_unpublish'); ?></th>
									<th><?php echo lang('posts_categories'); ?></th>
									<th></th>
								</thead>
								<tbody>
								<?php foreach($posts as $post_id => $post): ?>
									<tr>
										<td><?php echo html_escape($post->name); ?></td>
										<td><?php echo html_escape($post->publish); ?></td>
										<td><?php echo html_escape($post->unpublish); ?></td>
										<td>
											<?php foreach($categories[$post_id] as $category_id => $category_name): ?>
												<a href="<?php echo base_url('admin/posts/display/posts') . '/' . html_escape($category_id); ?>/"><span class="badge badge-primary"><?php echo html_escape($category_name); ?></span></a>
											<?php endforeach; ?>
										</td>
											<td>
												<div class="btn-group btn-group-sm">
													<?php
													echo icon_anchor('admin/posts/update/posts', $post_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit'),
														'class' => 'btn btn-default',
														)
													);
													?>
												</div>
											</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php else: ?>
								<p class="margin"><?php echo lang('posts_posts_empty'); ?></p>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');