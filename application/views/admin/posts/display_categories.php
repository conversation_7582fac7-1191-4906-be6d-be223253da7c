<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo anchor('admin/posts/create/categories', '<i class="fa fa-plus" aria-hidden="true"></i>',
					array(
					'title' => lang('add') . ' ' . lang('posts_category'),
					'class' => 'btn btn-primary',
					)
				);
				if( isset($category) )
				{
					echo anchor('admin/posts/display/categories', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				}
				?>
			</div>
			<h1>
				<?php echo lang('posts_categories'); ?>
				<small><?php echo isset($category) ? $category->name : ''; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('posts_categories'); ?></h3>
						</div>
						<div class="box-body no-padding">
							<?php // var_dump($category); ?>
							<?php if( ! empty($categories) ): ?>
							<table class="table table-striped no-data-table">
								<colspan>
									<col>
									<col width="85px">
								</colspan>
								<tbody>
								<?php foreach($categories as $category_id => $name): ?>
									<tr>
										<td>
											<?php
												if( $id === NULL )
													echo safe_anchor('admin/posts/display/categories', $category_id, $name);
												else
													echo html_escape($name);
											?>
										</td>
											<td align="right">
												<div class="btn-group btn-group-sm">
													<?php
													if( $id !== NULL )
													{
														echo icon_anchor('admin/posts/groups/categories', $category_id ,'<i class="fa fa-users" aria-hidden="true"></i>',
															array(
															'title' => lang('users_groups'),
															'class' => 'btn btn-default',
															)
														);
													}

													echo icon_anchor('admin/posts/update/categories', $category_id ,'<i class="fa fa-pencil" aria-hidden="true"></i>',
														array(
														'title' => lang('edit'),
														'class' => 'btn btn-default',
														)
													);
													?>
												</div>
											</td>
									</tr>
								<?php endforeach; ?>
								</tbody>
							</table>
							<?php else: ?>
								<p class="margin"><?php echo lang('posts_categories_empty'); ?></p>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');