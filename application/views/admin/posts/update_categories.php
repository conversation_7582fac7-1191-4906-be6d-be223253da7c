<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				if( $category->parent_id == NULL):
					echo anchor('admin/posts/display/categories', '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				else:
					echo icon_anchor('admin/posts/display/categories/', $category->parent_id, '<i class="fa fa-reply" aria-hidden="true"></i>',
						array(
						'title' => lang('cancel'),
						'class' => 'btn btn-default'
						)
					);
				endif;
				?>
			</div>
			<h1>
				<?php echo lang('posts_categories'); ?>
				<small><?php echo $category->name; ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit') . ' ' . lang('posts_category'); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('posts_category_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('posts_name'),'posts_name'); ?>
								<p class="form-text"><?php echo nl2br(lang('posts_name_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'posts_name',
											'value'	=> set_value('posts_name', $category->name),
											'class' => 'form-control'
										));
								?>
							</div>
							<div class="form-group">
								<?php echo form_label(lang('posts_category_slug'),'posts_category_slug'); ?>
								<p class="form-text"><?php echo nl2br(lang('posts_category_slug_help')); ?></p>
								<?php
									echo form_input(array(
											'name'	=> 'posts_category_slug',
											'value'	=> set_value('posts_category_slug', $category->slug),
											'class' => 'form-control'
										));
								?>
							</div>
							<?php if( ! empty($posts_category_parent) ): ?>
							<div class="form-group">
								<?php echo form_label(lang('posts_category_parent'),'posts_category_parent'); ?>
								<p class="form-text"><?php echo nl2br(lang('posts_category_parent_help')); ?></p>
								<?php
									echo form_dropdown('posts_category_parent', $posts_category_parent, set_value('posts_category_parent', $category->parent_id),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php endif; ?>
							<div class="form-group">
								<?php echo form_label(lang('posts_category_sticky'),'posts_category_sticky'); ?>
								<p class="form-text"><?php echo nl2br(lang('posts_category_sticky_help')); ?></p>
								<p class="form-text"><?php echo nl2br(lang('posts_category_sticky_help_extended')); ?></p>
								<?php
									echo form_dropdown('posts_category_sticky', $posts_category_sticky, set_value('posts_category_sticky', $category->sticky),array(
										'class' => 'form-control'
									));
								?>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');