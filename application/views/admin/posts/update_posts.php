<?php $this->load->view('template/header-editor'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				echo anchor('admin/posts', '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('edit') . ' ' . lang('posts_post'); ?>
				<small><?php echo $post->post_id; ?></small>
			</h1>
		</section>

		<!-- Main content -->
		<section class="content">
			<?php
			echo form_open_multipart(NULL,array(
				'id' => 'form-company-group',
				'autocomplete' => 'off'
			));
			echo form_hidden('uuid_kvalprak',$uuid_kvalprak);
			?>
			<div class="row">
				<div class="col-lg-9">
					<?php echo validation_errors();	?>
					<div class="box">
						<div class="box-header with-border">
							<?php
							echo form_input(array(
									'name'	=> 'posts_name',
									'value'	=> set_value('posts_name', $post->name),
									'class' => 'form-control',
									'placeholder' => lang('posts_name')
								));
							?>
						</div>
						<div class="box-body">
						<?php
							echo form_textarea(array(
									'name' => 'posts_document',
									'id' => 'posts_document',
									'value' => set_value('posts_document', $post->content, FALSE)
								));
						?>
						</div>
					</div>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('documents_attachments'); ?></h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-md-6">
									<div class="form-group file-upload no-margin">
										<div class="input-group">
											<span class="input-group-prepend"><i class="fa fa-file" aria-hidden="true"></i></span>
											<div id="dropzone" class="form-control dropzone"></div>
										</div>
									</div>
								</div>
								<div class="col-md-6">
									<?php if( !empty($attachments) ): ?>
									<table id="attachments-table" class="table table-striped table-bordered no-data-table">
										<colspan>
											<col>
											<col width="140px">
											<col width="100px">
										</colspan>
										<thead>
											<tr>
												<th>Filnamn</th>
												<th>Uppladdad</th>
												<th>Radera</th>
											</tr>
										</thead>
										<tbody>
											<?php foreach($attachments as $attachment): ?>
												<tr>
													<td><?php echo safe_anchor('admin/posts/download', $attachment->attachment_id, $attachment->file_name, ['target' => '_blank']); ?></td>
													<td><?php echo $attachment->uploaded_on; ?></td>
													<td>
														<div class="btn-group btn-group-sm">
															<?php
															echo form_button(array(
																	'title' => lang('delete'),
																	'class' => 'btn btn-default dialog-delete',
																	'data-id' => $attachment->attachment_id,
																	'data-title' => $attachment->file_name,
																	'data-dialog' => '#delete-dialog',
																	'data-url' => base_url('admin/posts/delete/attachments'),
																	'content' => '<i class="fa fa-trash" aria-hidden="true"></i> Radera'
																)
															);
															?>
														</div>
													</td>
												</tr>
											<?php endforeach; ?>
										</tbody>
									</table>
									<?php endif; ?>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- /.col-md-9-->
				<div class="col-lg-3">
					<div class="box collapsed-box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('posts_publish'); ?></h3>
							<div class="box-tools float-right">
								<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-plus"></i></button>
							</div>
						</div>
						<div class="box-body">
							<div class="form-group">
							<?php
								echo form_label(lang('posts_publish'),'posts_publish');
							?>
								<p class="form-text"><?php echo lang('posts_publish_help'); ?></p>
								<div class="input-group date">
									<div class="input-group-prepend">
										<i class="fa fa-calendar"></i>
									</div>
									<?php
										echo form_input(array(
												'name'	=> 'posts_publish',
												'value'	=> set_value('posts_publish', $post->publish),
												'class' => 'form-control float-right datepicker'
											));
									?>
									<span class="input-group-append">
										<button type="button" class="decrementDateByOneMonth btn btn-default btn-flat">-</button>
										<button type="button" class="incrementDateByOneMonth btn btn-default btn-flat">+</button>
									</span>
								</div>
								<!-- /.input group -->
							</div>
							<div class="form-group">
								<?php echo form_label(lang('posts_unpublish'),'posts_unpublish'); ?>
								<p class="form-text"><?php echo nl2br(lang('posts_unpublish_help')); ?></p>
								<div class="input-group date">
									<div class="input-group-prepend">
										<i class="fa fa-calendar"></i>
									</div>
									<?php
										echo form_input(array(
												'name'	=> 'posts_unpublish',
												'value'	=> set_value('posts_unpublish', $post->unpublish),
												'class' => 'form-control float-right datepicker'
											));
									?>
									<span class="input-group-append">
										<button type="button" class="decrementDateByOneMonth btn btn-default btn-flat">-</button>
										<button type="button" class="incrementDateByOneMonth btn btn-default btn-flat">+</button>
									</span>
								</div>
							</div>
						</div>
					</div>
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('posts_categories'); ?></h3>
						</div>
						<div class="box-body">
							<p class="form-text"><?php echo nl2br(lang('posts_category_help')); ?></p>
							<?php foreach($categories[0] AS $category_id => $category_name): ?>
								<div class="form-group">
									<?php if( isset($categories[$category_id]) ): ?>
										<strong><?php echo $category_name; ?></strong>
										<?php foreach($categories[$category_id] AS $sub_category_id => $sub_category_name): ?>
											<div class="checkbox indent">
												<label>
													<?php echo form_checkbox('posts_category[]', $sub_category_id, set_checkbox('posts_category[]', $sub_category_id, in_array($sub_category_id,$categories_checked))) . $sub_category_name; ?>
												</label>
											</div>
										<?php endforeach; ?>
									<?php else: ?>
										<div class="form-check">
											<label>
												<?php echo form_checkbox('posts_category[]', $category_id, set_checkbox('posts_category[]', $category_id, in_array($sub_category_id,$categories_checked))) . $category_name; ?>
											</label>
										</div>
									<?php endif; ?>
								</div>
							<?php endforeach; ?>
						</div>
					</div>
				</div>
				<!-- /.col-md-4-->
			</div>
			<!-- /.row -->
			<?php echo form_close(); ?>
		</section>
		<!-- /.content -->
		<div id="delete-dialog" style="display:none" title="Inget filnamn.">
			<i class="fa fa-warning"></i> Vill du ta bort filen permanent?
		</div>
	</div>
	<!-- /.content-wrapper -->
<?php $this->load->view('template/footer-posts'); ?>