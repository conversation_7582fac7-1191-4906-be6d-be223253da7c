<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php
				echo form_button(array(
					'type' => 'submit',
					'form' => 'form-company-group',
					'class' => 'btn btn-primary',
					'title' => lang('save'),
					'content' => '<i class="fa fa-save"></i>'
				));
				
				echo icon_anchor('admin/posts/display/categories', $category->parent_id, '<i class="fa fa-reply" aria-hidden="true"></i>',
					array(
					'title' => lang('cancel'),
					'class' => 'btn btn-default'
					)
				);
				?>
			</div>
			<h1>
				<?php echo lang('posts_categories'); ?>
				<small><?php echo lang('groups_groups'); ?></small>
			</h1>
		</section>
		<!-- Main content -->
		<section class="content">
			<div class="row">
				<div class="col-md-12">
					<div class="box">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('edit') . ' ' . lang('posts_category') . ': ' . html_escape($category->name); ?></h3>
						</div>
						<div class="box-body">
							<p><?php echo lang('menus_menu_desc'); ?></p>
							<?php 
								echo validation_errors();
								echo form_open(NULL,array(
									'id' => 'form-company-group',
									'autocomplete' => 'off'
								));
							?>
							<div class="form-group">
								<?php echo form_label(lang('groups_type_position'),'posts_category[]'); ?>
								<p class="form-text"><?php echo nl2br(lang('menus_type_position_write_help')); ?></p>
								<?php foreach($groups['position'] as $group_id => $group_name): ?>
								<div class="form-check">
									<label>
										<?php echo form_checkbox('posts_category[]', $group_id, set_checkbox('posts_category[]', $group_id, in_array($group_id,$groups_checked))) . $group_name; ?>
									</label>
								</div>
								<?php endforeach; ?>
							</div>
							<?php echo form_close(); ?>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /.box -->
				</div>
				<!-- /.col-md-12-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
<?php $this->load->view('template/footer');